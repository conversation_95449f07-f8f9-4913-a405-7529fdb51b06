# Reference: https://docs.docker.com/develop/develop-images/dockerfile_best-practices/
FROM micr.cloud.mioffice.cn/bd-qa/maven:3-jdk-8-onbuild AS builder

WORKDIR /home/<USER>/

COPY src /home/<USER>/src
COPY pom.xml /home/<USER>/pom.xml

# cp -a: Same as -pPR options. Preserves structure and attributes of files but not directory structure.
RUN mvn -U clean package -DskipTests && \
    mkdir -p /app/ && \
    cp -a target/hdfs-client-access-1.0.jar /app/hdfs-client-access-1.0.jar

FROM micr.cloud.mioffice.cn/bd-qa/xiaomi_centos:openjdk-1.8-krb5-1.14

WORKDIR /home/<USER>

COPY --from=builder /app/hdfs-client-access-1.0.jar ./

RUN yum update -y && \
    yum install -y vim && \
    yum clean all && \
    rm -rf /var/cache/yum