package com.xiaomi.hdfs;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.BufferedOutputStream;
import java.io.InputStream;

import java.net.URI;
import java.nio.charset.StandardCharsets;

public class Main {

  public static void main(String[] args) throws Exception {
    System.setProperty("java.security.krb5.conf", "src/main/resources/krb5.conf");
    Configuration conf = new Configuration();
    // 如果访问其它集群，不需要改变任何配置文件，只需要将hdfs://tjwqstaging-hdd替换
    // 例如：hdfs://zjyprc-hadoop
    FileSystem fs = FileSystem.get(new URI("hdfs://tjwqstaging-hdd"), conf);
    // 用户测试时，可能需要更换一个路径
    Path path = new Path("/tmp/test-access-hdfs");

    try (BufferedOutputStream bufferedOutputStream =
        new BufferedOutputStream(fs.create(path, true))) {
      byte[] bytes = "hello hdfs test-access-hdfs\n".getBytes(StandardCharsets.UTF_8);
      bufferedOutputStream.write(bytes);
    }

    try (InputStream in = fs.open(path)) {
      StringBuilder res = new StringBuilder();
      byte[] buffer = new byte[4096];
      int read;
      while ((read = in.read(buffer)) > 0) {
        res.append(new String(buffer, 0, read));
      }
      System.out.println(res);
    }
  }

}
