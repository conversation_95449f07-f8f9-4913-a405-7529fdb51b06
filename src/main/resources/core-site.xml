<configuration>
    <property>
        <name>configuration.service</name>
        <value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
    </property>
    <property>
        <name>configuration.service.name.team.id</name>
        <!-- 这里不要改动，不要改成自己的teamId -->
        <value>CL7202</value>
    </property>
    <property>
        <name>hadoop.security.authentication</name>
        <value>kerberos</value>
    </property>

    <!-- 新版本不再推荐使用keytab文件认证 -->
    <!--    <property>-->
    <!--        <name>hadoop.client.kerberos.principal</name>-->
    <!--        &lt;!&ndash; 需要替换为自己团队的kerberos_accounts &ndash;&gt;-->
    <!--        <value><EMAIL></value>-->
    <!--    </property>-->
    <!--    <property>-->
    <!--        <name>hadoop.client.keytab.file</name>-->
    <!--        &lt;!&ndash; 需要替换为自己团队的kerberos_accounts对应的keytab文件路径 &ndash;&gt;-->
    <!--        <value>/home/<USER>/hdfs_tst_admin.keytab</value>-->
    <!--    </property>-->

    <!-- 推荐：密码访问hdfs集群 -->
    <!--
        以下的三个信息分别是
        （获取方式：https://kdc.d.xiaomi.net/，点击具体的账户名）
        （sid的申请参考：https://cloud.mioffice.cn/next/console/keycenter/apply）

        注意：需要替换为自己团队的账户
        注意：JDK17 kerberos无法认证，推荐使用JDK8
     -->
    <property>
        <name>hadoop.client.kerberos.principal</name>
        <value><EMAIL></value>
    </property>
    <property>
        <name>hadoop.client.kerberos.encrypted.keycenter.sid</name>
        <value>s_grow_daxihongshi</value>
    </property>
    <property>
        <name>hadoop.client.kerberos.encrypted.password</name>
        <value>GBAqqz3aobl6WU4HdmIOfkplGBKbAFbmQg1FDICVk2kYD1IbhwEYEAv5IjdQ6E-duBwQCvkB6n8YFMjjuPfm_vkPJYnYX3EzW2FH7FckAA</value>
    </property>
</configuration>